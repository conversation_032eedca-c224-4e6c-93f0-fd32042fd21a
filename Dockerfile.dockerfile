FROM python:3.10

# for streamlit
ENV LC_ALL=C.UTF-8
ENV LANG=C.UTF-8
RUN mkdir -p /root/.streamlit

# expose 
EXPOSE 8501

#HEALTHCHECK CMD curl --fail http://localhost:8501/_stcore/health

WORKDIR /code
ADD requirements.txt .
RUN pip install --upgrade pip && pip install -r requirements.txt
COPY . .

#CMD streamlit run app2.py --server.port 8000 --server.enableCORS=true --server.enableXsrfProtection=false
ENTRYPOINT ["streamlit", "run", "app.py", "--server.port=8501", "--server.address=0.0.0.0"]
