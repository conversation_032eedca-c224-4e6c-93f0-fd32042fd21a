# INTENDED BEHAVIOR
from ionic import utils

# TOOLS:
# - build_agent: this should be the main 
# - build_ui
# - register_application
# - publish_application

# 

# Which space can leverage the most from technology?

# pip install "autogen-core"
# pip install -U "autogen-agentchat"
# pip install -U "autogen-agentchat" "autogen-ext[openai,azure]"

# preset agents
# UserProxyAgent, CodeExecutorAgent, OpenAIAssistantAgent, MultimodalWebSurfer, FileSurfer, VideoSurfer, 
#

pip install "autogen-ext[azure]"

import asyncio
from azure.core.credentials import AzureKeyCredential
from autogen_ext.models.azure import AzureAIChatCompletionClient
from autogen_core.models import UserMessage


async def main():
    client = AzureAIChatCompletionClient(
        endpoint="endpoint",
        credential=AzureKeyCredential("api_key"),
        model_info={
            "json_output": False,
            "function_calling": False,
            "vision": False,
            "family": "unknown",
        },
    )

    result = await client.create([UserMessage(content="What is the capital of France?", source="user")])
    print(result)


if __name__ == "__main__":
    asyncio.run(main())

from typing import Literal

from pydantic import BaseModel


# The response format for the agent as a Pydantic base model.
class AgentResponse(BaseModel):
    thoughts: str
    response: Literal["happy", "sad", "neutral"]


# Create an agent that uses the OpenAI GPT-4o model with the custom response format.
model_client = OpenAIChatCompletionClient(
    model="gpt-4o",
    response_format=AgentResponse,  # type: ignore
)
agent = AssistantAgent(
    "assistant",
    model_client=model_client,
    system_message="Categorize the input as happy, sad, or neutral following the JSON format.",
)

await Console(agent.run_stream(task="I am happy."))