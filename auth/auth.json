{"realm": "ionic", "enabled": true, "roles": {"realm": [{"name": "user-role", "description": "Default user role"}, {"name": "admin-role", "description": "Default admin role"}]}, "groups": [{"name": "user", "realmRoles": ["user-role"]}, {"name": "admin", "realmRoles": ["admin-role"]}], "clients": [{"clientId": "ionic", "enabled": true, "protocol": "openid-connect", "directAccessGrantsEnabled": true, "redirectUris": ["https://ui.domain.xyz/*"], "publicClient": true, "defaultRoles": ["user-role", "admin-role"]}], "users": [{"username": "jfarland", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Farland", "enabled": true, "requiredActions": [], "emailVerified": true, "credentials": [{"type": "password", "value": "pathfinder", "temporary": false}], "groups": ["admin"]}, {"username": "test_user", "email": "<EMAIL>", "firstName": "Test", "lastName": "User", "enabled": true, "requiredActions": [], "emailVerified": true, "credentials": [{"type": "password", "value": "pathfinder", "temporary": false}], "groups": ["user"]}]}