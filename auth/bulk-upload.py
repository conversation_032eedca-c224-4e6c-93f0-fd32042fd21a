import requests
import pandas as pd
import argparse
import os

# Create the argument parser
parser = argparse.ArgumentParser()
parser.add_argument("--file", type=str, help="File with credentials", required=True)
parser.add_argument("--u", type=str, help="Admin username", required=True)
parser.add_argument("--p", type=str, help="Admin password", required=True)

# Parse the arguments
args = parser.parse_args()

# Get the file extension
file_name, file_extension = os.path.splitext(args.file)

# Keycloak admin credentials
keycloak_url = 'my.keycloak.xyz'  # Update with your Keycloak URL
admin_username = args.u
admin_password = args.p
realm_name = 'personafai'

# Obtain access token
def get_access_token():
    token_url = f'{keycloak_url}/realms/master/protocol/openid-connect/token'
    data = {
        'grant_type': 'password',
        'client_id': 'admin-cli',
        'username': admin_username,
        'password': admin_password
    }
    response = requests.post(token_url, data=data)
    return response.json().get('access_token')

# Create a user
def create_user(username, password, email, access_token):
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    user_data = {
        "username": username,
        "email": email,
        "firstName": "first",
        "lastName": "last",
        "requiredActions": [],
        "emailVerified": True,
        "enabled": True,
        "credentials": [{
            "type": "password",
            "value": password,
            "temporary": False
        }],
        "groups": ["user"]
    }
    create_url = f'{keycloak_url}/admin/realms/{realm_name}/users'
    response = requests.post(create_url, json=user_data, headers=headers)
    if response.status_code == 201:
        print(f"User {username} created successfully.")
    else:
        print(f"Failed to create user {username}: {response.text}")


# Bulk create users
if __name__ == "__main__":
    access_token = get_access_token()

    if file_extension.lower() == '.csv':
        df = pd.read_csv(args.file)
    elif file_extension.lower() == '.xlsx':
        df = pd.read_excel(args.file)
        
    users = df[['username', 'password', 'email']].to_dict(orient='records')

    for user in users:
        create_user(user['username'], user['password'], user['email'], access_token)
