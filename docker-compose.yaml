services:

  ionic_ui:
    image: #personafai
    networks:
      - ionic_network
    build:
      context: .
      dockerfile: Dockerfile.dockerfile
    environment:
      IONIC_AUTH_URL: ionic_auth
      IONIC_AUTH_PORT: 8080
      AUTH_SERVER_URL: ${AUTH_SERVER_URL}
      CLIENT_ID: ${CLIENT_ID}
      REALM_NAME: ${REALM_NAME}
      DB_USER: ${DB_USER}
      DB_KEY: ${DB_KEY}
      TOGETHER_API_KEY: ${TOGETHER_API_KEY}
      FINNGPT35_KEY: ${FINNGPT35_KEY}
      FINNGPT4_KEY: ${FINNGPT4_KEY}
      FINNLLAMA3_KEY: ${FINNLLAMA3_KEY}
      FINNGPT4O_KEY: ${FINNGPT4O_KEY}
      FINNGPT4OM_KEY: ${FINNGPT4OM_KEY}
      BING_API_KEY: ${BING_API_KEY}
      DOCKER_HOST: ${DOCKER_HOST}

    depends_on:
      - ionic_auth
    ports:
      - 8501:8501
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock # mount location of docker daemon (docker host) to make sure UI can access

  ionic_db:
    image: postgres:latest
    networks:
      - ionic_network
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_DB: ${POSTGRES_DB}
      TOGETHER_API_KEY: ${TOGETHER_API_KEY}
    ports:
      - 5432:5432
    volumes:
      - postgres_data:/var/lib/postgresql/data  # Mount the named volume

  ionic_memgraph:
    image: memgraph/memgraph-mage:latest
    networks:
      - ionic_network
    ports:
      - "7687:7687"
      - "7444:7444"
    command: ["--log-level=TRACE"]

  ionic_graphlab:
    image: memgraph/lab:latest
    networks:
      - ionic_network
    ports:
      - "3000:3000"
    depends_on:
      - ionic_memgraph
    environment:
      - QUICK_CONNECT_MG_HOST=pfai_memgraph
      - QUICK_CONNECT_MG_PORT=7687
  
  ionic_vector:
    image: chromadb/chroma:0.5.13
    volumes:
      - ./chromadb:/chroma/chroma
    environment:
      - IS_PERSISTENT=TRUE
      - PERSIST_DIRECTORY=/chroma/chroma # this is the default path, change it as needed
      - ANONYMIZED_TELEMETRY=${ANONYMIZED_TELEMETRY:-TRUE}
    ports:
      - 8000:8000
    networks:
      - pfai_network

  pfai_auth:
    image: quay.io/keycloak/keycloak:24.0.3
    build:
      context: .
      dockerfile: ./auth/Keycloak.dockerfile
    networks:
      - ionic_network
    environment:
      KEYCLOAK_ADMIN: ${KEYCLOAK_ADMIN}
      KEYCLOAK_ADMIN_PASSWORD: ${KEYCLOAK_ADMIN_PASSWORD}
      # Can be important to tell keycloak what it's host is
      KC_HOSTNAME: ${KC_HOSTNAME}
      # Enable HTTP if using ngrok for tunneling + HTTPS
      KC_HTTP_ENABLED: "true"
      KC_HTTP_PORT: 8080
      
    ports:
      - 8080:8080
    
    command: 
      - "start"
      - "--import-realm"
      - "--verbose"

    volumes:
      - ./auth/pfai.json:/opt/keycloak/data/import/pfai.json

  ngrok:
      image: ngrok/ngrok:latest
      networks:
        - ionic_network      
      environment:
        - AUTH_TOKEN
        - PFAI_URL
        - AUTH_URL
      volumes:
        - ./start-ngrok.sh:/start-ngrok.sh
      ports:
        - 4040:4040
      entrypoint: ["bash", "/start-ngrok.sh"]

volumes:
  postgres_data:
    driver: local

networks:
  ionic_network:
    driver: bridge