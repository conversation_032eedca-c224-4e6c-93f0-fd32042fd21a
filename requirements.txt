accelerate==0.34.2
aiohappyeyeballs==2.4.0
aiohttp==3.10.5
aiosignal==1.3.1
altair==5.2.0
annotated-types==0.6.0
anyio==4.3.0
arxiv==2.1.3
asgiref==3.8.1
asttokens==2.4.1
async-property==0.2.2
async-timeout==4.0.3
attrs==23.2.0
#autogen-agentchat~=0.2
autopep8==2.0.4
backoff==2.2.1
bcrypt==4.2.0
beautifulsoup4==4.12.3
blinker==1.7.0
build==1.2.2
cachetools==5.3.2
certifi==2024.2.2
cffi==1.16.0
charset-normalizer==3.3.2
chroma-hnswlib==0.7.6
chromadb==0.5.13
click==8.1.7
coloredlogs==15.0.1
comm==0.2.2
contourpy==1.3.0
cryptography==42.0.3
cycler==0.12.1
dataclasses-json==0.6.7
datasets==3.0.0
debugpy==1.8.5
decorator==5.1.1
Deprecated==1.2.14
deprecation==2.1.0
diffusers @ git+https://github.com/huggingface/diffusers.git@8cdcdd9e32925200ce5e1cf410fe14a774f3c3a6
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
docker==7.1.0
docopt==0.6.2
docutils==0.20.1
docx==0.2.4
duckduckgo_search==6.2.13
entrypoints==0.4
et-xmlfile==1.1.0
eval_type_backport==0.2.0
exceptiongroup==1.2.2
executing==2.0.1
Faker==30.0.0
fastapi==0.114.2
favicon==0.7.0
feedparser==6.0.11
filelock==3.15.4
FLAML==2.1.1
flatbuffers==24.3.25
fonttools==4.54.1
frozenlist==1.4.1
fsspec==2024.6.1
gitdb==4.0.11
GitPython==3.1.42
google-auth==2.34.0
googleapis-common-protos==1.65.0
greenlet==3.1.0
grpcio==1.66.1
h11==0.14.0
htbuilder==0.6.2
httpcore==1.0.3
httptools==0.6.1
httpx==0.27.2
huggingface-hub==0.24.6
humanfriendly==10.0
idna==3.6
importlib-metadata==7.0.1
importlib_resources==6.4.5
ipykernel==6.29.5
ipython==8.26.0
jaraco.classes==3.3.1
jedi==0.19.1
jeepney==0.8.0
Jinja2==3.1.3
joblib==1.4.2
jsonpatch==1.33
jsonpointer==3.0.0
jsonschema==4.21.1
jsonschema-specifications==2023.12.1
jupyter_client==8.6.2
jupyter_core==5.7.2
jwcrypto==1.5.6
keyring==24.3.0
kiwisolver==1.4.7
kubernetes==30.1.0
langchain==0.3.0
langchain-community==0.3.0
langchain-core==0.3.1
langchain-text-splitters==0.3.0
langsmith==0.1.121
lxml==5.3.0
Markdown==3.7
markdown-it-py==3.0.0
markdownify==0.13.1
markdownlit==0.0.7
MarkupSafe==2.1.5
marshmallow==3.22.0
matplotlib==3.9.2
matplotlib-inline==0.1.7
mdurl==0.1.2
mmh3==4.1.0
monotonic==1.6
more-itertools==10.2.0
mpmath==1.3.0
multidict==6.0.5
multiprocess==0.70.16
mypy-extensions==1.0.0
nest-asyncio==1.6.0
networkx==3.3
nh3==0.2.15
numpy==1.26.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==*********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==**********
nvidia-cusparse-cu12==**********
nvidia-nccl-cu12==2.20.5
nvidia-nvjitlink-cu12==12.6.20
nvidia-nvtx-cu12==12.1.105
oauthlib==3.2.2
onnxruntime==1.19.2
openai==1.12.0
openpyxl==3.1.2
opentelemetry-api==1.27.0
opentelemetry-exporter-otlp-proto-common==1.27.0
opentelemetry-exporter-otlp-proto-grpc==1.27.0
opentelemetry-instrumentation==0.48b0
opentelemetry-instrumentation-asgi==0.48b0
opentelemetry-instrumentation-fastapi==0.48b0
opentelemetry-proto==1.27.0
opentelemetry-sdk==1.27.0
opentelemetry-semantic-conventions==0.48b0
opentelemetry-util-http==0.48b0
optimum==1.22.0
orjson==3.10.7
overrides==7.7.0
packaging==24.1
pandas==2.2.0
parso==0.8.4
pexpect==4.9.0
pillow==10.4.0
pkginfo==1.9.6
platformdirs==4.2.2
plotly==5.24.1
posthog==3.6.6
primp==0.6.3
prometheus_client==0.21.0
prompt_toolkit==3.0.47
protobuf==4.25.3
psutil==6.0.0
psycopg2-binary==2.9.9
ptyprocess==0.7.0
pure_eval==0.2.3
pyarrow==15.0.0
pyasn1==0.6.1
pyasn1_modules==0.4.1
pyautogen==0.3.0
pycodestyle==2.11.1
pycparser==2.21
pydantic==2.9.0
pydantic-settings==2.5.2
pydantic_core==2.23.2
pydeck==0.8.1b0
Pygments==2.18.0
pymdown-extensions==10.11
pyparsing==3.1.4
pypdf==4.3.1
PyPDF2==3.0.1
PyPika==0.48.9
pyproject_hooks==1.1.0
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-keycloak==4.3.0
pytz==2024.1
PyYAML==6.0.1
pyzmq==26.2.0
readme_renderer==43.0
referencing==0.33.0
regex==2023.12.25
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rfc3986==2.0.0
rich==13.7.1
rpds-py==0.18.0
rsa==4.9
safetensors==0.4.4
scikit-learn==1.5.2
scipy==1.14.1
SecretStorage==3.3.3
sentence-transformers==3.1.0
sentencepiece==0.2.0
sgmllib3k==1.0.0
shellingham==1.5.4
six==1.16.0
smmap==5.0.1
sniffio==1.3.0
soupsieve==2.6
SQLAlchemy==2.0.35
st-annotated-text==4.0.1
st-theme==1.2.3
stack-data==0.6.3
starlette==0.38.5
streamlit==1.35.0
streamlit-camera-input-live==0.2.0
streamlit-card==1.0.2
streamlit-embedcode==0.1.2
streamlit-extras==0.4.7
streamlit-faker==0.0.3
streamlit-image-coordinates==0.1.9
streamlit-image-select==0.6.0
streamlit-keycloak==1.1.1
streamlit-keyup==0.2.4
streamlit-toggle-switch==1.0.2
streamlit-vertical-slider==2.5.5
sympy==1.13.2
tabulate==0.9.0
tenacity==8.2.3
termcolor==2.4.0
threadpoolctl==3.5.0
tiktoken==0.6.0
together==1.2.11
tokenizers==0.19
toml==0.10.2
tomli==2.0.1
toolz==0.12.1
torch==2.3.0
tornado==6.4.1
tqdm==4.66.5
traitlets==5.14.3
transformers==4.44.0
triton==2.3.0
twine==5.0.0
typer==0.12.5
typing-inspect==0.9.0
typing_extensions==4.12.2
tzdata==2024.1
urllib3==2.2.1
uvicorn==0.30.6
uvloop==0.20.0
validators==0.34.0
watchdog==4.0.0
watchfiles==0.24.0
wcwidth==0.2.13
websocket-client==1.8.0
websockets==13.0.1
wrapt==1.16.0
xxhash==3.5.0
yarl==1.10.0
zipp==3.20.0
